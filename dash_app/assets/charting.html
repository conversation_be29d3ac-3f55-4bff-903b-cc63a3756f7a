<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8" />
<meta name="viewport" content="width=device-width,initial-scale=1" />
<title>Lightweight Charts — Lines + Fib 50%</title>
<link rel="stylesheet" href="charting.css">
</head>
<body>
  <div class="top-controls">
    <div class="controls-row">
      <button id="mode-normal" class="btn active" title="Normal chart viewing mode">📊 Normal</button>
      <button id="mode-line" class="btn" title="Click to place single line">➕ Line</button>
      <button id="mode-trendline" class="btn" title="Click and drag to draw a diagonal trendline">📈 Trendline</button>
      <button id="mode-fib" class="btn" title="MouseDown set P1, drag, MouseUp set P2, creates retracement and take profit levels">∿ Fib</button>
      <button id="mode-rect" class="btn" title="Click and drag to draw a rectangle">▭ Rectangle</button>
      <button id="auto-fib" class="btn" title="Automatically draw Fibonacci retracement from most recent swing">🤖 Auto-Fib</button>
      <button id="clear" class="btn" title="Remove all lines">🧹 Clear</button>
      <button id="drawings-toggle" class="lines-toggle" title="Show/Hide Drawings Panel">📋 Drawings</button>
    </div>
  </div>

  <div class="info-section">
    <div class="mode-info">
      <h4>Current Mode</h4>
      <div class="muted" id="hint">Mode: Normal — chart viewing mode with pan and zoom enabled.</div>
    </div>
    <div class="trade-details">
      <h4>Trade Details</h4>
      <div id="trade-info" class="muted">Draw a Fibonacci retracement to generate trade plan</div>
    </div>
  </div>

  <div id="chart"></div>

  <div id="drawings-panel" class="drawings-panel">
    <strong>Drawings</strong>
    <ul id="list"></ul>
  </div>

  <!-- TradingView Lightweight Charts (MIT) -->
  <script src="https://unpkg.com/lightweight-charts@4.2.2/dist/lightweight-charts.standalone.production.js"></script>
  <script src="charting.js">></script>
</body>
</html>
