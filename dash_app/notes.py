# app.py
from dash import Dash, html, dcc

app = Dash(__name__)
app.layout = html.Div(
    style={"height": "100vh", "display": "grid", "gridTemplateRows": "1fr auto", "background": "#0e1116"},
    children=[
        html.Iframe(
            src="assets/charting.html",
            style={"width": "100%", "height": "100%", "border": "0"}
        ),
        html.Div("Prices will also log in the browser console.", style={"color": "#8b949e", "padding": "6px 10px"})
    ]
)
if __name__ == "__main__":
    app.run(debug=False)